import serial
import serial.tools.list_ports
from PyQt5.QtCore import QObject, pyqtSignal, QThread
import logging
from typing import List, Optional

logger = logging.getLogger(__name__)

class SerialWorker(QThread):
    data_received = pyqtSignal(bytes)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, serial_port: serial.Serial):
        super().__init__()
        self.serial_port = serial_port
        self.is_running = False
        
    def run(self):
        self.is_running = True
        while self.is_running and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    self.data_received.emit(data)
                self.msleep(10)
            except Exception as e:
                self.error_occurred.emit(str(e))
                break
                
    def stop(self):
        self.is_running = False
        self.wait()

class SerialHandler(QObject):
    data_received = pyqtSignal(bytes)
    connection_status_changed = pyqtSignal(bool)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.serial_port: Optional[serial.Serial] = None
        self.worker: Optional[SerialWorker] = None
        
    @staticmethod
    def get_available_ports() -> List[str]:
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    def connect(self, port: str, baudrate: int = 9600, 
                data_bits: int = 8, stop_bits: int = 1, 
                parity: str = 'N') -> bool:
        try:
            if self.serial_port and self.serial_port.is_open:
                self.disconnect()
                
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=data_bits,
                stopbits=stop_bits,
                parity=parity,
                timeout=0.1
            )
            
            self.worker = SerialWorker(self.serial_port)
            self.worker.data_received.connect(self._on_data_received)
            self.worker.error_occurred.connect(self._on_error)
            self.worker.start()
            
            self.connection_status_changed.emit(True)
            logger.info(f"Connected to {port} at {baudrate} bps")
            return True
            
        except Exception as e:
            error_msg = f"Failed to connect to {port}: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def disconnect(self):
        try:
            if self.worker:
                self.worker.stop()
                self.worker = None
                
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                
            self.connection_status_changed.emit(False)
            logger.info("Disconnected from serial port")
            
        except Exception as e:
            error_msg = f"Error during disconnection: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
    
    def send_data(self, data: bytes) -> bool:
        if not self.serial_port or not self.serial_port.is_open:
            self.error_occurred.emit("Serial port is not connected")
            return False
            
        try:
            self.serial_port.write(data)
            logger.debug(f"Sent data: {data.hex()}")
            return True
        except Exception as e:
            error_msg = f"Failed to send data: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def _on_data_received(self, data: bytes):
        logger.debug(f"Received data: {data.hex()}")
        self.data_received.emit(data)
    
    def _on_error(self, error: str):
        self.error_occurred.emit(error)
        self.disconnect()
    
    def is_connected(self) -> bool:
        return self.serial_port is not None and self.serial_port.is_open