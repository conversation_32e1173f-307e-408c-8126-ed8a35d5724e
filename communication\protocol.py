from dataclasses import dataclass
from typing import Optional, Tuple
import struct

@dataclass
class ProtocolFrame:
    address: int
    frequency_mhz: float
    
    FRAME_START = 0xA5
    FRAME_END = 0xAA
    DEFAULT_ADDRESS = 0xA0
    
    def to_bytes(self) -> bytes:
        freq_str = f"{self.frequency_mhz:013.6f}"
        freq_parts = freq_str.split('.')
        
        integer_part = freq_parts[0].zfill(5)
        decimal_part = freq_parts[1].ljust(6, '0')[:6]
        
        bcd_bytes = []
        for i in range(0, 5, 2):
            if i < len(integer_part) - 1:
                high = int(integer_part[i])
                low = int(integer_part[i + 1])
                bcd_bytes.append((high << 4) | low)
            else:
                bcd_bytes.append(int(integer_part[-1]))
        
        for i in range(0, 6, 2):
            high = int(decimal_part[i])
            low = int(decimal_part[i + 1])
            bcd_bytes.append((high << 4) | low)
        
        while len(bcd_bytes) < 6:
            bcd_bytes.append(0x00)
        
        bcd_bytes = bcd_bytes[:6]
        
        frame = bytearray()
        frame.append(self.FRAME_START)
        frame.append(self.address)
        frame.extend(bcd_bytes)
        frame.append(self.FRAME_END)
        
        return bytes(frame)
    
    @classmethod
    def from_bytes(cls, data: bytes) -> Optional['ProtocolFrame']:
        if len(data) != 9:
            return None
            
        if data[0] != cls.FRAME_START or data[-1] != cls.FRAME_END:
            return None
            
        address = data[1]
        bcd_data = data[2:8]
        
        freq_str = ""
        for byte in bcd_data:
            high = (byte >> 4) & 0x0F
            low = byte & 0x0F
            freq_str += f"{high}{low}"
        
        try:
            integer_part = freq_str[:5]
            decimal_part = freq_str[5:11]
            frequency = float(f"{integer_part}.{decimal_part}")
            
            return cls(address=address, frequency_mhz=frequency)
        except ValueError:
            return None

class ProtocolEncoder:
    @staticmethod
    def encode_frequency_command(frequency_mhz: float, address: int = 0xA0) -> bytes:
        frame = ProtocolFrame(address=address, frequency_mhz=frequency_mhz)
        return frame.to_bytes()
    
    @staticmethod
    def encode_dual_frequency_command(freq1_mhz: float, freq2_mhz: float) -> Tuple[bytes, bytes]:
        frame1 = ProtocolFrame(address=0xA0, frequency_mhz=freq1_mhz)
        frame2 = ProtocolFrame(address=0xA1, frequency_mhz=freq2_mhz)
        return frame1.to_bytes(), frame2.to_bytes()
    
    @staticmethod
    def decode_frame(data: bytes) -> Optional[ProtocolFrame]:
        return ProtocolFrame.from_bytes(data)
    
    @staticmethod
    def validate_frequency(frequency_mhz: float) -> bool:
        return 0 <= frequency_mhz <= 99999.999999