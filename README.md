# 串口上位机程序 - 本振模块控制

基于Python + PyQt5开发的串口通信上位机程序，用于控制本振模块。

## 功能特点

1. **串口通信**
   - 支持RS422/RS485通信
   - 自动检测可用串口
   - 实时显示通信数据
   - 支持十六进制/ASCII显示切换

2. **协议实现**
   - 完全符合通信协议规范
   - 支持单频率和双频率模式
   - BCD编码自动转换
   - 帧格式自动封装

3. **用户界面**
   - 简洁直观的操作界面
   - 实时通信日志显示
   - 支持单频率和双频率控制
   - 可调节地址参数

## 安装说明

1. 安装Python 3.8或更高版本

2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行程序：
```bash
python main.py
```

2. **操作步骤**：
   - 选择串口和波特率（默认9600）
   - 点击"连接"建立通信
   - 输入频率值
   - 选择地址（默认0xA0）
   - 点击"发送单频率"或"发送双频率"

## 通信协议

- 帧格式：起始(0xA5) + 地址(1B) + 参数(6B) + 帧尾(0xAA)
- 波特率：9600bps
- 数据位：8位，1位停止位，无校验

## 频率范围

支持0-99999.999999 MHz范围内的频率设置

## 日志记录

程序运行日志保存在`logs`目录下，便于问题排查。