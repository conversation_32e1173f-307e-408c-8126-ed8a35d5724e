from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QComboBox, QLabel, QLineEdit,
                             QTextEdit, QGroupBox, QGridLayout, QMessageBox,
                             QCheckBox, QSpinBox, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSlot, QTimer
from PyQt5.QtGui import QFont, QTextCursor
import logging
from datetime import datetime

from communication.serial_handler import SerialHandler
from communication.protocol import ProtocolEncoder
from gui.local_oscillator_panel import LocalOscillatorPanel

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.serial_handler = SerialHandler()
        self.protocol_encoder = ProtocolEncoder()
        
        self.init_ui()
        self.setup_connections()
        self.refresh_ports()
        
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.refresh_ports)
        self.auto_refresh_timer.start(2000)
        
    def init_ui(self):
        self.setWindowTitle("串口上位机程序 - 本振模块控制")
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        self.setup_ui_elements(main_layout)
        
    def setup_ui_elements(self, layout):
        
        conn_group = QGroupBox("串口连接")
        conn_layout = QHBoxLayout()
        conn_group.setLayout(conn_layout)
        
        conn_layout.addWidget(QLabel("串口:"))
        self.port_combo = QComboBox()
        conn_layout.addWidget(self.port_combo)
        
        self.refresh_btn = QPushButton("刷新")
        conn_layout.addWidget(self.refresh_btn)
        
        conn_layout.addWidget(QLabel("波特率:"))
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baudrate_combo.setCurrentText("9600")
        conn_layout.addWidget(self.baudrate_combo)
        
        self.connect_btn = QPushButton("连接")
        self.connect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        conn_layout.addWidget(self.connect_btn)
        
        conn_layout.addStretch()
        
        layout.addWidget(conn_group)
        
        self.lo_panel = LocalOscillatorPanel()
        self.lo_panel.frequency_set_requested.connect(self.send_lo_frequency)
        layout.addWidget(self.lo_panel)
        
        comm_group = QGroupBox("通信记录")
        comm_layout = QVBoxLayout()
        comm_group.setLayout(comm_layout)
        
        self.hex_display_check = QCheckBox("十六进制显示")
        self.hex_display_check.setChecked(True)
        comm_layout.addWidget(self.hex_display_check)
        
        self.comm_log = QTextEdit()
        self.comm_log.setReadOnly(True)
        self.comm_log.setFont(QFont("Consolas", 9))
        comm_layout.addWidget(self.comm_log)
        
        clear_layout = QHBoxLayout()
        self.clear_btn = QPushButton("清空记录")
        clear_layout.addStretch()
        clear_layout.addWidget(self.clear_btn)
        comm_layout.addLayout(clear_layout)
        
        layout.addWidget(comm_group)
        
        
    def setup_connections(self):
        self.refresh_btn.clicked.connect(self.refresh_ports)
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.clear_btn.clicked.connect(self.clear_log)
        
        self.serial_handler.data_received.connect(self.on_data_received)
        self.serial_handler.connection_status_changed.connect(self.on_connection_status_changed)
        self.serial_handler.error_occurred.connect(self.on_error)
        
    @pyqtSlot()
    def refresh_ports(self):
        current_port = self.port_combo.currentText()
        self.port_combo.clear()
        ports = SerialHandler.get_available_ports()
        self.port_combo.addItems(ports)
        
        if current_port in ports:
            self.port_combo.setCurrentText(current_port)
            
    @pyqtSlot()
    def toggle_connection(self):
        if self.serial_handler.is_connected():
            self.serial_handler.disconnect()
        else:
            port = self.port_combo.currentText()
            baudrate = int(self.baudrate_combo.currentText())
            
            if not port:
                QMessageBox.warning(self, "警告", "请选择串口")
                return
                
            self.serial_handler.connect(port, baudrate)
            
    @pyqtSlot()
    def clear_log(self):
        self.comm_log.clear()
        
    @pyqtSlot(bytes)
    def on_data_received(self, data):
        self.log_communication("RX", data)
        
    @pyqtSlot(bool)
    def on_connection_status_changed(self, connected):
        if connected:
            self.connect_btn.setText("断开")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
            self.port_combo.setEnabled(False)
            self.baudrate_combo.setEnabled(False)
        else:
            self.connect_btn.setText("连接")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            self.port_combo.setEnabled(True)
            self.baudrate_combo.setEnabled(True)
            
    @pyqtSlot(str)
    def on_error(self, error):
        QMessageBox.critical(self, "错误", error)
        
    @pyqtSlot(int, float, int)
    def send_lo_frequency(self, channel_id, frequency, address):
        try:
            if not self.serial_handler.is_connected():
                QMessageBox.warning(self, "警告", "请先连接串口")
                return
                
            if not self.protocol_encoder.validate_frequency(frequency):
                QMessageBox.warning(self, "警告", f"通道 {channel_id} 频率超出范围 (0-99999.999999 MHz)")
                return
                
            data = self.protocol_encoder.encode_frequency_command(frequency, address)
            
            if self.serial_handler.send_data(data):
                self.log_communication("TX", data)
                logger.info(f"已发送通道 {channel_id} 频率: {frequency} MHz 到地址 0x{address:02X}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送失败: {str(e)}")
        
    def log_communication(self, direction, data):
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        if self.hex_display_check.isChecked():
            data_str = ' '.join(f'{b:02X}' for b in data)
        else:
            data_str = data.decode('ascii', errors='replace')
            
        color = "#0000FF" if direction == "TX" else "#FF0000"
        log_entry = f'<span style="color: {color}">[{timestamp}] {direction}: {data_str}</span><br>'
        
        cursor = self.comm_log.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertHtml(log_entry)
        self.comm_log.setTextCursor(cursor)
        self.comm_log.ensureCursorVisible()