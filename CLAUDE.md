# 项目开发进度记录

## 项目概述
本项目是一个串口上位机程序，用于控制三路本振模块。

## 已完成功能

### 1. 三路本振控制界面
- 创建了独立的本振控制面板 (`gui/local_oscillator_panel.py`)
- 实现了三路本振的平铺显示，每路对应不同地址：
  - 通道1 (LO1): 地址 0xA0, 频率范围 1575-1670 MHz
  - 通道2 (LO2): 地址 0xA1, 频率范围 1750-1850 MHz
  - 通道3 (LO3): 地址 0xA2, 频率范围 2200-2300 MHz
- 根据通信协议规定，各通道有独立的频率范围限制

### 2. 多种频率控制方式
- **滑块控制**: 可视化拖动调节
- **数值框控制**: 精确输入，支持滚轮微调
- **快速步进按钮**: -100, -10, -1, +1, +10, +100 MHz
- **批量操作**: 
  - "发送所有通道"按钮
  - "重置所有频率"按钮（重置各通道为其频率范围的中间值）

### 3. 界面优化
- 移除了原有的传统控制界面，保持简洁
- 串口连接控件改为单行显示
- 去掉了重复的频率输入框功能

### 4. 即时发送功能
- 滚轮调节频率后立即发送
- 步进按钮点击后立即发送
- 所有频率调节操作都会实时发送到对应地址

## 技术实现细节

### 通信协议
- 使用现有的 `ProtocolEncoder` 类进行数据编码
- 支持单频率和双频率发送模式
- 数据格式遵循既定的通信协议

### GUI架构
- 基于 PyQt5 框架
- 模块化设计，本振控制面板独立于主窗口
- 使用信号槽机制实现组件间通信

### 关键文件
1. `gui/local_oscillator_panel.py` - 三路本振控制界面
2. `gui/main_window.py` - 主窗口，集成了本振控制面板
3. `communication/protocol.py` - 通信协议实现
4. `communication/serial_handler.py` - 串口通信处理

## 使用说明
1. 选择串口并设置波特率
2. 点击"连接"按钮建立串口连接
3. 使用任意控制方式调节频率
4. 频率会自动发送到对应的本振模块

## 注意事项
- 各通道频率范围根据通信协议限制：
  - LO1: 1575-1670 MHz
  - LO2: 1750-1850 MHz
  - LO3: 2200-2300 MHz
- 需要先建立串口连接才能发送数据
- 所有频率调节操作都会立即触发数据发送

## 更新历史
### 2025-01-07
- 根据新的通信协议更新了各通道的频率范围限制
- 更新了重置功能，使各通道重置为其频率范围的中间值