### RS422/RS485
- 通信方式：异步, 全双工。
- 字符格式： 1 位起始位，8 位数据位，1 位停止位、无奇偶校验；
- 波特率：9600bps

### 帧格式设计
异步串口通信信息帧为变频通道模块工作参数控制命令帧
控制命令由MCP发出，由本振模块接收。控制命令帧定义参见表1。
##### 表1 本振模块命令帧描述表
| 字段名称 | 帧起始  | 地址  | 参数  | 帧尾   |
| ---- | ---- | --- | --- | ---- |
| 字段标识 | FSB  | ADB | PRB | FEB  |
| 填充内容 | 0xA5 | 见说明 | 见说明 | 0xAA |
| 字节数  | 1B   | 1B  | 6B  | 1B   |
说明：
- FSB：帧起始字段（1字节），规定为0xA5。
- ADB：地址（1字节），没有默认为0xA0。
- PRB：参数字段（6字节），工作参数。
- FEB：帧尾字段（1字节），规定为0xAA。
2）参数
	工作参数以BCD编码方式表示，高位在前。
	比如工作频率2230.567800MHz，表示为00 22 30 56 78 00
	比如工作频率11245.123MHz，表示为01 12 45 12 30 00
	针对一个本振模块2路本振的模块，当做两个本振模块处理，即本振1地址为0xA0，本振2地址为0xA1，比如模块频率输出，频率1为128000.000MHz，频率2为3400.000MHz
	发送的命令为2条：
	A5 A0 01 28 00 00 00 00 AA
	A5 A1 00 34 00 00 00 00 AA


此本振模块具有3路本振，频率范围分别为
LO1：1575~1670M
LO2：1750~1850M
LO3：2200~2300M