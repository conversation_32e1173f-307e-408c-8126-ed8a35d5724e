from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QLabel, QPushButton, QGridLayout,
                             QSlider, QSpinBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont
import logging

logger = logging.getLogger(__name__)

class LocalOscillatorChannel(QGroupBox):
    frequency_changed = pyqtSignal(int, float)
    send_requested = pyqtSignal(int, float)
    
    def __init__(self, channel_id, title, address, min_freq=1500, max_freq=2300):
        super().__init__(title)
        self.channel_id = channel_id
        self.address = address
        self.min_freq = min_freq
        self.max_freq = max_freq
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        freq_layout = QHBoxLayout()
        freq_layout.addWidget(QLabel("频率 (MHz):"))
        
        self.freq_spinbox = QSpinBox()
        self.freq_spinbox.setRange(self.min_freq, self.max_freq)
        self.freq_spinbox.setValue((self.min_freq + self.max_freq) // 2)
        self.freq_spinbox.setSuffix(" MHz")
        self.freq_spinbox.valueChanged.connect(self.on_spinbox_changed)
        self.freq_spinbox.wheelEvent = self.handle_wheel_event
        freq_layout.addWidget(self.freq_spinbox)
        
        layout.addLayout(freq_layout)
        
        self.freq_slider = QSlider(Qt.Horizontal)
        self.freq_slider.setRange(self.min_freq, self.max_freq)
        self.freq_slider.setValue((self.min_freq + self.max_freq) // 2)
        self.freq_slider.setTickPosition(QSlider.TicksBelow)
        self.freq_slider.setTickInterval(100)
        self.freq_slider.valueChanged.connect(self.on_slider_changed)
        layout.addWidget(self.freq_slider)
        
        slider_labels_layout = QHBoxLayout()
        slider_labels_layout.addWidget(QLabel(str(self.min_freq)))
        slider_labels_layout.addStretch()
        slider_labels_layout.addWidget(QLabel(str((self.min_freq + self.max_freq) // 2)))
        slider_labels_layout.addStretch()
        slider_labels_layout.addWidget(QLabel(str(self.max_freq)))
        layout.addLayout(slider_labels_layout)
        
        step_buttons_layout = QHBoxLayout()
        step_values = [-100, -10, -1, 1, 10, 100]
        for step in step_values:
            btn = QPushButton(f"{step:+d}")
            btn.setMaximumWidth(60)
            if step > 0:
                btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            else:
                btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
            btn.clicked.connect(lambda checked, s=step: self.step_frequency(s))
            step_buttons_layout.addWidget(btn)
        layout.addLayout(step_buttons_layout)
        
        self.send_btn = QPushButton(f"发送到地址 0x{self.address:02X}")
        self.send_btn.clicked.connect(self.on_send_clicked)
        self.send_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        layout.addWidget(self.send_btn)
        
        info_layout = QHBoxLayout()
        self.current_freq_label = QLabel(f"当前频率: {self.freq_spinbox.value()} MHz")
        self.current_freq_label.setFont(QFont("Arial", 10, QFont.Bold))
        info_layout.addWidget(self.current_freq_label)
        info_layout.addStretch()
        
        self.address_label = QLabel(f"地址: 0x{self.address:02X}")
        info_layout.addWidget(self.address_label)
        
        layout.addLayout(info_layout)
        
    @pyqtSlot(int)
    def on_slider_changed(self, value):
        self.freq_spinbox.blockSignals(True)
        self.freq_spinbox.setValue(value)
        self.freq_spinbox.blockSignals(False)
        self.update_frequency_display(value)
        self.frequency_changed.emit(self.channel_id, value)
        
    @pyqtSlot(int)
    def on_spinbox_changed(self, value):
        self.freq_slider.blockSignals(True)
        self.freq_slider.setValue(value)
        self.freq_slider.blockSignals(False)
        self.update_frequency_display(value)
        self.frequency_changed.emit(self.channel_id, value)
        
    def handle_wheel_event(self, event):
        super(QSpinBox, self.freq_spinbox).wheelEvent(event)
        self.on_send_clicked()
        
    @pyqtSlot()
    def on_send_clicked(self):
        freq = self.freq_spinbox.value()
        self.send_requested.emit(self.channel_id, float(freq))
        
    def update_frequency_display(self, freq):
        self.current_freq_label.setText(f"当前频率: {freq} MHz")
        
    def get_frequency(self):
        return float(self.freq_spinbox.value())
        
    def set_frequency(self, freq):
        if self.min_freq <= freq <= self.max_freq:
            self.freq_spinbox.setValue(int(freq))
            self.freq_slider.setValue(int(freq))
            self.update_frequency_display(int(freq))
            
    def step_frequency(self, step):
        current_freq = self.freq_spinbox.value()
        new_freq = current_freq + step
        if self.min_freq <= new_freq <= self.max_freq:
            self.set_frequency(new_freq)
            self.on_send_clicked()

class LocalOscillatorPanel(QWidget):
    frequency_set_requested = pyqtSignal(int, float, int)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        title_label = QLabel("三路本振控制面板")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        channels_layout = QHBoxLayout()
        channels_layout.setSpacing(10)
        
        self.channel1 = LocalOscillatorChannel(1, "本振通道 1 (LO1)", 0xA0, 1575, 1670)
        self.channel1.send_requested.connect(self.on_channel_send_requested)
        channels_layout.addWidget(self.channel1)
        
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        channels_layout.addWidget(separator1)
        
        self.channel2 = LocalOscillatorChannel(2, "本振通道 2 (LO2)", 0xA1, 1750, 1850)
        self.channel2.send_requested.connect(self.on_channel_send_requested)
        channels_layout.addWidget(self.channel2)
        
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        channels_layout.addWidget(separator2)
        
        self.channel3 = LocalOscillatorChannel(3, "本振通道 3 (LO3)", 0xA2, 2200, 2300)
        self.channel3.send_requested.connect(self.on_channel_send_requested)
        channels_layout.addWidget(self.channel3)
        
        main_layout.addLayout(channels_layout)
        
        control_layout = QHBoxLayout()
        
        self.send_all_btn = QPushButton("发送所有通道")
        self.send_all_btn.clicked.connect(self.send_all_channels)
        self.send_all_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        control_layout.addWidget(self.send_all_btn)
        
        self.reset_btn = QPushButton("重置所有频率")
        self.reset_btn.clicked.connect(self.reset_all_channels)
        self.reset_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px; }")
        control_layout.addWidget(self.reset_btn)
        
        main_layout.addLayout(control_layout)
        
    @pyqtSlot(int, float)
    def on_channel_send_requested(self, channel_id, frequency):
        if channel_id == 1:
            address = 0xA0
        elif channel_id == 2:
            address = 0xA1
        else:
            address = 0xA2
            
        self.frequency_set_requested.emit(channel_id, frequency, address)
        logger.info(f"请求发送通道 {channel_id} 频率: {frequency} MHz, 地址: 0x{address:02X}")
        
    @pyqtSlot()
    def send_all_channels(self):
        for channel_id in range(1, 4):
            channel = getattr(self, f'channel{channel_id}')
            freq = channel.get_frequency()
            if channel_id == 1:
                address = 0xA0
            elif channel_id == 2:
                address = 0xA1
            else:
                address = 0xA2
            self.frequency_set_requested.emit(channel_id, freq, address)
            
    @pyqtSlot()
    def reset_all_channels(self):
        self.channel1.set_frequency(1622)  # (1575 + 1670) // 2
        self.channel2.set_frequency(1800)  # (1750 + 1850) // 2
        self.channel3.set_frequency(2250)  # (2200 + 2300) // 2
        logger.info("所有通道频率已重置为各自的中间频率")